<?php

namespace app\modules\worker\controllers;

use app\modules\worker\services\WorkerService;
use app\modules\worker\filters\AuthFilter;
use app\modules\worker\models\Worker;
use yii\web\Response;
use yii\web\UploadedFile;

/**
 * API контроллер профиля работника
 */
class ProfileController extends BaseApiController
{
    /**
     * @var WorkerService
     */
    private $workerService;

    /**
     * @var Worker|null Текущий авторизованный работник
     */
    public $currentWorker;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();
        $this->workerService = new WorkerService();
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        $behaviors = parent::behaviors();

        // Все действия профиля требуют авторизации
        $behaviors['auth'] = [
            'class' => AuthFilter::class,
            'authRequired' => [
                'profile/index',
                'profile/update',
                'profile/upload-audio'
            ]
        ];

        return $behaviors;
    }

    /**
     * Получить профиль работника
     *
     * GET /worker/profile
     *
     * @return Response
     */
    public function actionIndex()
    {
        if (!$this->currentWorker) {
            return $this->sendUnauthorized($this->t('app', 'Authentication required'));
        }

        try {
            $profile = $this->workerService->getWorkerProfileById($this->currentWorker->id);

            if (!$profile) {
                return $this->sendNotFound($this->t('app', 'Profile not found'));
            }

            // Логируем просмотр профиля
            \app\common\models\WorkerLog::logAction(
                $this->currentWorker->chat_id,
                'profile_view',
                ['worker_id' => $this->currentWorker->id],
                $this->currentWorker->id
            );

            return $this->sendSuccess(
                $profile,
                $this->t('app', 'Profile retrieved successfully')
            );
        } catch (\Exception $e) {
            \Yii::error('Error getting worker profile: ' . $e->getMessage(), 'worker-api');
            return $this->sendError($this->t('app', 'Error retrieving profile'));
        }
    }

    /**
     * Обновить профиль работника
     *
     * PUT /worker/profile
     *
     * @return Response
     */
    public function actionUpdate()
    {
        if (!$this->currentWorker) {
            return $this->sendUnauthorized($this->t('app', 'Authentication required'));
        }

        $request = \Yii::$app->request;

        // Получаем данные для обновления
        $data = [];

        if ($request->post('name') !== null) {
            $data['name'] = trim($request->post('name'));
        }

        if ($request->post('age') !== null) {
            $data['age'] = (int) $request->post('age');
        }

        if ($request->post('experience_years') !== null) {
            $data['experience_years'] = (int) $request->post('experience_years');
        }

        if ($request->post('about') !== null) {
            $data['about'] = trim($request->post('about'));
        }

        if ($request->post('language') !== null) {
            $data['language'] = trim($request->post('language'));
        }

        // Обновляем местоположение
        if ($request->post('lat') !== null && $request->post('lng') !== null) {
            $data['lat'] = (float) $request->post('lat');
            $data['lng'] = (float) $request->post('lng');
        }

        // Обновляем профессии
        if ($request->post('profession_ids') !== null) {
            $professionIds = $request->post('profession_ids');
            if (is_string($professionIds)) {
                $professionIds = explode(',', $professionIds);
            }
            $data['profession_ids'] = array_map('intval', array_filter($professionIds));
        }

        // Валидация
        $errors = $this->validateProfileData($data);
        if (!empty($errors)) {
            return $this->sendValidationError($errors);
        }

        try {
            $result = $this->workerService->updateWorkerProfile($this->currentWorker->phone, $data);

            if ($result === false) {
                return $this->sendError($this->t('app', 'Failed to update profile'));
            }

            if (isset($result['errors'])) {
                return $this->sendValidationError($result['errors']);
            }

            // Логируем обновление профиля
            \app\common\models\WorkerLog::logAction(
                $this->currentWorker->chat_id,
                'profile_update',
                [
                    'updated_fields' => array_keys($data),
                    'worker_id' => $this->currentWorker->id
                ],
                $this->currentWorker->id
            );

            return $this->sendSuccess(
                $result,
                $this->t('app', 'Profile updated successfully')
            );
        } catch (\Exception $e) {
            \Yii::error('Error updating worker profile: ' . $e->getMessage(), 'worker-api');
            return $this->sendError($this->t('app', 'Error updating profile'));
        }
    }

    /**
     * Загрузить аудиофайл
     *
     * POST /worker/profile/upload-audio
     *
     * @return Response
     */
    public function actionUploadAudio()
    {
        if (!$this->currentWorker) {
            return $this->sendUnauthorized($this->t('app', 'Authentication required'));
        }

        $audioFile = UploadedFile::getInstanceByName('audio');

        if (!$audioFile) {
            return $this->sendValidationError([
                'audio' => [$this->t('app', 'Audio file is required')]
            ]);
        }

        try {
            $result = $this->workerService->uploadAudioFile($this->currentWorker->phone, $audioFile);

            if ($result === false) {
                return $this->sendError($this->t('app', 'Failed to upload audio file'));
            }

            if (isset($result['error'])) {
                return $this->sendValidationError([
                    'audio' => [$this->t('app', $result['error'])]
                ]);
            }

            // Логируем загрузку аудио
            \app\common\models\WorkerLog::logAction(
                $this->currentWorker->chat_id,
                'audio_upload',
                [
                    'file_name' => $audioFile->name,
                    'file_size' => $audioFile->size,
                    'audio_url' => $result['audio_url']
                ],
                $this->currentWorker->id
            );

            return $this->sendSuccess(
                $result,
                $this->t('app', 'Audio file uploaded successfully')
            );
        } catch (\Exception $e) {
            \Yii::error('Error uploading audio file: ' . $e->getMessage(), 'worker-api');
            return $this->sendError($this->t('app', 'Error uploading audio file'));
        }
    }

    /**
     * Валидация данных профиля
     *
     * @param array $data
     * @return array
     */
    private function validateProfileData($data)
    {
        $errors = [];

        // Валидация имени
        if (isset($data['name'])) {
            if (empty($data['name'])) {
                $errors['name'] = [$this->t('app', 'Name cannot be empty')];
            } elseif (mb_strlen($data['name']) > 100) {
                $errors['name'] = [$this->t('app', 'Name is too long')];
            }
        }

        // Валидация возраста
        if (isset($data['age'])) {
            if ($data['age'] < 16 || $data['age'] > 80) {
                $errors['age'] = [$this->t('app', 'Age must be between 16 and 80')];
            }
        }

        // Валидация опыта работы
        if (isset($data['experience_years'])) {
            if ($data['experience_years'] < 0 || $data['experience_years'] > 50) {
                $errors['experience_years'] = [$this->t('app', 'Experience must be between 0 and 50 years')];
            }
        }

        // Валидация языка
        if (isset($data['language'])) {
            $allowedLanguages = ['ru', 'uz', 'en'];
            if (!in_array($data['language'], $allowedLanguages)) {
                $errors['language'] = [$this->t('app', 'Invalid language')];
            }
        }

        // Валидация координат
        if (isset($data['lat']) || isset($data['lng'])) {
            if (!isset($data['lat']) || !isset($data['lng'])) {
                $errors['location'] = [$this->t('app', 'Both latitude and longitude are required')];
            } elseif ($data['lat'] < -90 || $data['lat'] > 90) {
                $errors['lat'] = [$this->t('app', 'Invalid latitude')];
            } elseif ($data['lng'] < -180 || $data['lng'] > 180) {
                $errors['lng'] = [$this->t('app', 'Invalid longitude')];
            }
        }

        return $errors;
    }
}