<?php

return [
    // General messages
    'Phone number is required' => 'Phone number is required',
    'Worker with this phone number not found' => 'Worker with this phone number not found',
    'Worker not found' => 'Worker not found',
    'Successfully authenticated' => 'Successfully authenticated',
    'Token not provided' => 'Token not provided',
    'Successfully logged out' => 'Successfully logged out',
    'Invalid or expired token' => 'Invalid or expired token',
    'Token is valid' => 'Token is valid',
    'Token refreshed successfully' => 'Token refreshed successfully',
    'Authentication required' => 'Authentication required',
    
    // Vacancies
    'Vacancies retrieved successfully' => 'Vacancies retrieved successfully',
    'Error retrieving vacancies' => 'Error retrieving vacancies',
    'Search completed successfully' => 'Search completed successfully',
    'Error searching vacancies' => 'Error searching vacancies',
    'Invalid vacancy ID' => 'Invalid vacancy ID',
    'Vacancy not found' => 'Vacancy not found',
    'Vacancy details retrieved successfully' => 'Vacancy details retrieved successfully',
    'Error retrieving vacancy details' => 'Error retrieving vacancy details',
    
    // Profile
    'Profile not found' => 'Profile not found',
    'Profile retrieved successfully' => 'Profile retrieved successfully',
    'Error retrieving profile' => 'Error retrieving profile',
    'Failed to update profile' => 'Failed to update profile',
    'Profile updated successfully' => 'Profile updated successfully',
    'Error updating profile' => 'Error updating profile',
    
    // Profile validation
    'Name cannot be empty' => 'Name cannot be empty',
    'Name is too long' => 'Name is too long',
    'Age must be between 16 and 80' => 'Age must be between 16 and 80',
    'Experience must be between 0 and 50 years' => 'Experience must be between 0 and 50 years',
    'Invalid language' => 'Invalid language',
    'Both latitude and longitude are required' => 'Both latitude and longitude are required',
    'Invalid latitude' => 'Invalid latitude',
    'Invalid longitude' => 'Invalid longitude',
    
    // Audio
    'Audio file is required' => 'Audio file is required',
    'Failed to upload audio file' => 'Failed to upload audio file',
    'Invalid file type' => 'Invalid file type',
    'File too large' => 'File too large',
    'Audio file uploaded successfully' => 'Audio file uploaded successfully',
    'Error uploading audio file' => 'Error uploading audio file',
    
    // General errors
    'Validation failed' => 'Validation failed',
    'Resource not found' => 'Resource not found',
    'Unauthorized' => 'Unauthorized',
    'Forbidden' => 'Forbidden',
];
