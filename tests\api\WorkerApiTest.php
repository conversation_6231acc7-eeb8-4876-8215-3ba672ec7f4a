<?php

namespace tests\api;

use app\modules\worker\models\Worker;
use app\modules\worker\services\VacancyService;
use app\modules\worker\services\WorkerService;
use app\common\services\ApiResponse;

/**
 * Тестирование API endpoints модуля worker
 */
class WorkerApiTest
{
    /**
     * Тестирование ApiResponse класса
     */
    public function testApiResponse()
    {
        echo "=== Тестирование ApiResponse ===\n";
        
        // Тест успешного ответа
        $successResponse = ApiResponse::success(['test' => 'data'], 'Success message');
        $successArray = $successResponse->toArray();
        
        echo "Успешный ответ:\n";
        echo json_encode($successArray, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
        
        // Тест ответа с ошибкой
        $errorResponse = ApiResponse::error('Error message', null, 400);
        $errorArray = $errorResponse->toArray();
        
        echo "Ответ с ошибкой:\n";
        echo json_encode($errorArray, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
        
        // Тест пагинированного ответа
        $paginatedResponse = ApiResponse::paginated(
            [['id' => 1, 'title' => 'Test Item']],
            100,
            1,
            20,
            'Paginated data'
        );
        $paginatedArray = $paginatedResponse->toArray();
        
        echo "Пагинированный ответ:\n";
        echo json_encode($paginatedArray, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";
    }

    /**
     * Тестирование VacancyService
     */
    public function testVacancyService()
    {
        echo "=== Тестирование VacancyService ===\n";
        
        $vacancyService = new VacancyService();
        
        // Тест получения списка вакансий
        try {
            $result = $vacancyService->getVacanciesList(1, 10);
            echo "Список вакансий:\n";
            echo "Всего: " . $result['total'] . "\n";
            echo "Страница: " . $result['page'] . "\n";
            echo "На странице: " . $result['per_page'] . "\n";
            echo "Элементов: " . count($result['items']) . "\n\n";
        } catch (\Exception $e) {
            echo "Ошибка при получении списка вакансий: " . $e->getMessage() . "\n\n";
        }
        
        // Тест поиска вакансий
        try {
            $result = $vacancyService->searchVacancies('программист', 1, 10);
            echo "Поиск вакансий по запросу 'программист':\n";
            echo "Найдено: " . $result['total'] . "\n";
            echo "Элементов: " . count($result['items']) . "\n\n";
        } catch (\Exception $e) {
            echo "Ошибка при поиске вакансий: " . $e->getMessage() . "\n\n";
        }
    }

    /**
     * Тестирование WorkerService
     */
    public function testWorkerService()
    {
        echo "=== Тестирование WorkerService ===\n";
        
        $workerService = new WorkerService();
        
        // Тест проверки существования работника
        $exists = $workerService->workerExists('+998901234567');
        echo "Работник с телефоном +998901234567 существует: " . ($exists ? 'Да' : 'Нет') . "\n\n";
    }

    /**
     * Тестирование модели Worker
     */
    public function testWorkerModel()
    {
        echo "=== Тестирование модели Worker ===\n";
        
        // Создаем тестового работника
        $worker = new Worker();
        $worker->name = 'Тестовый Работник';
        $worker->phone = '+998901234567';
        $worker->age = 25;
        $worker->language = 'ru';
        
        // Тестируем валидацию
        if ($worker->validate()) {
            echo "Валидация модели Worker: Успешно\n";
        } else {
            echo "Валидация модели Worker: Ошибки:\n";
            foreach ($worker->errors as $field => $errors) {
                echo "  $field: " . implode(', ', $errors) . "\n";
            }
        }
        
        // Тестируем генерацию токена
        $token = $worker->generateAuthToken();
        echo "Сгенерированный токен: " . substr($token, 0, 20) . "...\n";
        echo "Время истечения: " . $worker->token_expires_at . "\n";
        echo "Токен валиден: " . ($worker->isTokenValid() ? 'Да' : 'Нет') . "\n\n";
    }

    /**
     * Тестирование мультиязычности
     */
    public function testMultilingual()
    {
        echo "=== Тестирование мультиязычности ===\n";
        
        // Тестируем переводы для разных языков
        $languages = ['ru', 'uz', 'en'];
        $testMessage = 'Phone number is required';
        
        foreach ($languages as $language) {
            $translation = \Yii::t('app', $testMessage, [], $language);
            echo "[$language] $testMessage => $translation\n";
        }
        echo "\n";
    }

    /**
     * Запуск всех тестов
     */
    public function runAllTests()
    {
        echo "Запуск тестов API модуля worker\n";
        echo "================================\n\n";
        
        $this->testApiResponse();
        $this->testVacancyService();
        $this->testWorkerService();
        $this->testWorkerModel();
        $this->testMultilingual();
        
        echo "Все тесты завершены!\n";
    }
}

// Запуск тестов если файл вызван напрямую
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    // Инициализация Yii приложения для тестов
    require_once __DIR__ . '/../../vendor/autoload.php';
    require_once __DIR__ . '/../../vendor/yiisoft/yii2/Yii.php';
    
    $config = require __DIR__ . '/../../config/web.php';
    new \yii\web\Application($config);
    
    $test = new WorkerApiTest();
    $test->runAllTests();
}
