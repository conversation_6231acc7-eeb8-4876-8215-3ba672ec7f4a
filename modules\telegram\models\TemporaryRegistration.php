<?php

namespace app\modules\telegram\models;

use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;

/**
 * Модель временной регистрации
 * 
 * @property int $id
 * @property string $chat_id
 * @property string $name
 * @property string $phone
 * @property int $age
 * @property float $lat
 * @property float $long
 * @property int $experience_years
 * @property string $audio_file_url
 * @property string $about
 * @property string $language
 * @property int $status
 * @property string $created_at
 * @property string $updated_at
 */
class TemporaryRegistration extends ActiveRecord
{
    // Этапы регистрации
    const STEP_START = 0;
    const STEP_LANGUAGE = 1;
    const STEP_NAME = 2;
    const STEP_PHONE = 3;
    const STEP_LOCATION = 4;
    const STEP_AGE = 5;
    const STEP_PROFESSION = 6;  // WebView
    const STEP_AUDIO = 7;
    const STEP_CONFIRMATION = 8;
    const STEP_ADDITIONAL_INFO = 9;
    const STEP_COMPLETED = 10;

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%temporary_registrations}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => 'updated_at',
                'value' => function() {
                    return date('Y-m-d H:i:s');
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['chat_id'], 'required'],
            [['age', 'experience_years', 'status'], 'integer'],
            [['lat', 'long'], 'number'],
            [['about'], 'string'],
            [['created_at', 'updated_at'], 'safe'],
            [['chat_id'], 'string', 'max' => 50],
            [['name'], 'string', 'max' => 100],
            [['phone'], 'string', 'max' => 20],
            [['audio_file_url'], 'string', 'max' => 255],
            [['language'], 'string', 'max' => 10],
            [['chat_id'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'chat_id' => 'Telegram Chat ID',
            'name' => 'Имя',
            'phone' => 'Телефон',
            'age' => 'Возраст',
            'lat' => 'Широта',
            'long' => 'Долгота',
            'experience_years' => 'Опыт работы (лет)',
            'audio_file_url' => 'Аудиофайл',
            'about' => 'О себе',
            'language' => 'Язык',
            'status' => 'Этап регистрации',
            'created_at' => 'Дата создания',
            'updated_at' => 'Дата обновления',
        ];
    }

    /**
     * Получить пользователя по chat_id
     */
    public static function findByChatId($chatId)
    {
        return static::findOne(['chat_id' => $chatId]);
    }

    /**
     * Создать или обновить регистрацию
     */
    public static function createOrUpdate($chatId, $data = [])
    {
        $model = static::findByChatId($chatId);
        if (!$model) {
            $model = new static();
            $model->chat_id = $chatId;
            $model->status = static::STEP_START;
        }

        foreach ($data as $key => $value) {
            if ($model->hasAttribute($key)) {
                $model->$key = $value;
            }
        }

        return $model->save() ? $model : false;
    }

    /**
     * Получить названия этапов
     */
    public static function getStepLabels()
    {
        return [
            static::STEP_START => 'Начало',
            static::STEP_LANGUAGE => 'Выбор языка',
            static::STEP_NAME => 'Ввод имени',
            static::STEP_PHONE => 'Ввод телефона',
            static::STEP_LOCATION => 'Геолокация',
            static::STEP_AGE => 'Возраст',
            static::STEP_PROFESSION => 'Выбор профессии',
            static::STEP_AUDIO => 'Аудиозапись',
            static::STEP_CONFIRMATION => 'Подтверждение',
            static::STEP_ADDITIONAL_INFO => 'Дополнительная информация',
            static::STEP_COMPLETED => 'Завершено',
        ];
    }

    /**
     * Перейти к следующему этапу
     */
    public function nextStep()
    {
        $this->status++;
        return $this->save();
    }
} 