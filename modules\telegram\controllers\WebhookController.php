<?php

namespace app\modules\telegram\controllers;

use yii\web\Controller;
use yii\web\Response;
use app\common\models\WorkerLog;

/**
 * Контроллер для обработки Telegram webhook
 */
class WebhookController extends Controller
{
    /**
     * @var array Отключение CSRF для webhook
     */
    public $enableCsrfValidation = false;

    /**
     * Обработка входящих webhook от Telegram
     * @return Response
     */
    public function actionIndex()
    {
        $input = file_get_contents('php://input');
        $update = json_decode($input, true);
        
        if (!$update) {
            return $this->asJson(['ok' => false]);
        }

        \Yii::info('Telegram webhook: ' . $input, 'telegram');

        // Получаем chat_id
        $chatId = $this->getChatId($update);
        if (!$chatId) {
            return $this->as<PERSON>son(['ok' => false]);
        }

        try {
            // Логируем входящее обновление
            $this->logIncomingUpdate($chatId, $update);
        } catch (\Exception $e) {
            \Yii::error("Failed to log incoming update: " . $e->getMessage(), 'telegram');
        }

        // Проверяем, есть ли активная регистрация
        $registration = \app\modules\telegram\models\TemporaryRegistration::findByChatId($chatId);
        
        if ($registration && $registration->status < \app\modules\telegram\models\TemporaryRegistration::STEP_COMPLETED) {
            // Перенаправляем на обработку регистрации
            return $this->forward('/telegram/registration/process');
        }

        // Обычная логика бота для зарегистрированных пользователей
        if (isset($update['message']['text']) && $update['message']['text'] === '/start') {
            $this->sendWelcomeMessage($chatId);
        }
        
        return $this->asJson(['ok' => true]);
    }

    /**
     * Получение chat_id из update
     */
    private function getChatId($update)
    {
        if (isset($update['message']['chat']['id'])) {
            return $update['message']['chat']['id'];
        }
        
        if (isset($update['callback_query']['from']['id'])) {
            return $update['callback_query']['from']['id'];
        }

        return null;
    }

    /**
     * Отправка приветственного сообщения
     */
    private function sendWelcomeMessage($chatId)
    {
        $worker = \app\modules\worker\models\Worker::findOne(['chat_id' => $chatId]);
        
        if ($worker) {
            $text = "👋 Добро пожаловать обратно, {$worker->name}!\n\n" .
                   "🔍 Используйте меню для поиска вакансий и управления профилем.";
        } else {
            $text = "👋 Добро пожаловать в IshTop!\n\n" .
                   "Для начала работы пройдите регистрацию командой /start";
        }

        // Здесь отправка сообщения через TelegramService
        \Yii::info("Send welcome to {$chatId}: {$text}", 'telegram');
        
        // Логируем отправку приветственного сообщения
        WorkerLog::logAction($chatId, 'welcome_message_sent', [
            'message_text' => $text,
            'user_type' => $worker ? 'existing_worker' : 'new_user'
        ], $worker ? $worker->id : null);
    }

    /**
     * Логирование входящего обновления
     */
    private function logIncomingUpdate($chatId, $update)
    {
        // Определяем тип сообщения
        if (isset($update['message'])) {
            $message = $update['message'];
            $messageType = 'text';
            $messageData = [];

            if (isset($message['text'])) {
                $messageType = 'text';
                $messageData['text'] = $message['text'];
            } elseif (isset($message['voice'])) {
                $messageType = 'voice';
                $messageData['file_id'] = $message['voice']['file_id'];
                $messageData['duration'] = $message['voice']['duration'];
            } elseif (isset($message['location'])) {
                $messageType = 'location';
                $messageData['latitude'] = $message['location']['latitude'];
                $messageData['longitude'] = $message['location']['longitude'];
            } elseif (isset($message['contact'])) {
                $messageType = 'contact';
                $messageData['phone_number'] = $message['contact']['phone_number'];
                $messageData['first_name'] = $message['contact']['first_name'];
            } elseif (isset($message['photo'])) {
                $messageType = 'photo';
                $messageData['file_id'] = end($message['photo'])['file_id'];
            }

            // Логируем сообщение
            WorkerLog::logIncomingMessage($chatId, $messageType, $messageData);

        } elseif (isset($update['callback_query'])) {
            $callbackQuery = $update['callback_query'];
            
            // Логируем callback query
            WorkerLog::logCallback($chatId, $callbackQuery['data'], [
                'message_id' => $callbackQuery['message']['message_id'] ?? null,
                'inline_message_id' => $callbackQuery['inline_message_id'] ?? null
            ]);
        }
    }
} 