<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Выбор деятельности</title>
    <script src="https://telegram.org/js/telegram-web-app.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: var(--tg-theme-bg-color, #ffffff);
            color: var(--tg-theme-text-color, #000000);
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .search-box {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--tg-theme-hint-color, #999999);
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 20px;
            box-sizing: border-box;
        }
        
        .profession-list {
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .profession-item {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid var(--tg-theme-hint-color, #eeeeee);
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .profession-item:hover {
            background-color: var(--tg-theme-secondary-bg-color, #f0f0f0);
        }
        
        .profession-item.selected {
            background-color: var(--tg-theme-button-color, #007aff);
            color: var(--tg-theme-button-text-color, #ffffff);
        }
        
        .profession-checkbox {
            margin-right: 12px;
        }
        
        .profession-name {
            font-size: 16px;
            font-weight: 500;
        }
        
        .no-results {
            text-align: center;
            padding: 40px;
            color: var(--tg-theme-hint-color, #999999);
        }
        
        .submit-button {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: var(--tg-theme-button-color, #007aff);
            color: var(--tg-theme-button-text-color, #ffffff);
            border: none;
            padding: 15px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            display: none;
        }
        
        .submit-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>🔧 Выберите деятельность</h2>
        <p>Выберите одну или несколько профессий</p>
    </div>
    
    <input type="text" class="search-box" placeholder="🔍 Поиск профессий..." id="searchInput">
    
    <div class="profession-list" id="professionList">
        <?php foreach ($professions as $profession): ?>
            <div class="profession-item" data-id="<?= $profession->id ?>">
                <input type="checkbox" class="profession-checkbox" id="prof_<?= $profession->id ?>">
                <label for="prof_<?= $profession->id ?>" class="profession-name"><?= htmlspecialchars($profession->name) ?></label>
            </div>
        <?php endforeach; ?>
    </div>
    
    <div class="no-results" id="noResults" style="display: none;">
        Профессии не найдены
    </div>
    
    <button class="submit-button" id="submitButton" onclick="submitSelection()">
        Выбрать профессии
    </button>

    <script>
        // Инициализация Telegram Web App
        let tg = window.Telegram.WebApp;
        tg.expand();
        
        const chatId = '<?= $chat_id ?>';
        const searchInput = document.getElementById('searchInput');
        const professionList = document.getElementById('professionList');
        const noResults = document.getElementById('noResults');
        const submitButton = document.getElementById('submitButton');
        
        let selectedProfessions = [];
        
        // Поиск профессий
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            const items = professionList.querySelectorAll('.profession-item');
            let visibleCount = 0;
            
            items.forEach(item => {
                const name = item.querySelector('.profession-name').textContent.toLowerCase();
                if (name.includes(query)) {
                    item.style.display = 'flex';
                    visibleCount++;
                } else {
                    item.style.display = 'none';
                }
            });
            
            noResults.style.display = visibleCount === 0 ? 'block' : 'none';
        });
        
        // Обработка выбора профессий
        professionList.addEventListener('click', function(e) {
            const item = e.target.closest('.profession-item');
            if (!item) return;
            
            const checkbox = item.querySelector('.profession-checkbox');
            const professionId = item.dataset.id;
            
            checkbox.checked = !checkbox.checked;
            
            if (checkbox.checked) {
                item.classList.add('selected');
                selectedProfessions.push(professionId);
            } else {
                item.classList.remove('selected');
                selectedProfessions = selectedProfessions.filter(id => id !== professionId);
            }
            
            updateSubmitButton();
        });
        
        // Обновление кнопки отправки
        function updateSubmitButton() {
            if (selectedProfessions.length > 0) {
                submitButton.style.display = 'block';
                submitButton.textContent = `Выбрать (${selectedProfessions.length})`;
            } else {
                submitButton.style.display = 'none';
            }
        }
        
        // Отправка выбранных профессий
        function submitSelection() {
            if (selectedProfessions.length === 0) return;
            
            submitButton.disabled = true;
            submitButton.textContent = 'Сохраняем...';
            
            fetch('/telegram/profession/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `chat_id=${chatId}&profession_ids[]=${selectedProfessions.join('&profession_ids[]=')}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    tg.showAlert(`Выбрана профессия: ${data.profession}`, function() {
                        tg.close();
                    });
                } else {
                    tg.showAlert(data.message || 'Ошибка сохранения');
                    submitButton.disabled = false;
                    submitButton.textContent = `Выбрать (${selectedProfessions.length})`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                tg.showAlert('Ошибка соединения');
                submitButton.disabled = false;
                submitButton.textContent = `Выбрать (${selectedProfessions.length})`;
            });
        }
    </script>
</body>
</html> 