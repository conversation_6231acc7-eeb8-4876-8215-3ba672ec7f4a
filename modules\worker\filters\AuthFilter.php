<?php

namespace app\modules\worker\filters;

use yii\base\ActionFilter;
use yii\web\UnauthorizedHttpException;
use app\modules\worker\models\Worker;
use app\common\services\ApiResponse;

/**
 * Фильтр авторизации для API endpoints модуля worker
 */
class AuthFilter extends ActionFilter
{
    /**
     * @var array Действия, которые требуют авторизации
     */
    public $authRequired = [];

    /**
     * @var array Действия, которые не требуют авторизации (публичные)
     */
    public $publicActions = [];

    /**
     * {@inheritdoc}
     */
    public function beforeAction($action)
    {
        $actionId = $action->id;
        $controllerId = $action->controller->id;
        $fullActionId = $controllerId . '/' . $actionId;

        // Проверяем, требует ли действие авторизации
        if ($this->isAuthRequired($fullActionId)) {
            $worker = $this->authenticateUser();
            
            if (!$worker) {
                $this->sendUnauthorizedResponse();
                return false;
            }

            // Сохраняем авторизованного пользователя в контроллере
            $action->controller->currentWorker = $worker;
        }

        return parent::beforeAction($action);
    }

    /**
     * Проверить, требует ли действие авторизации
     * 
     * @param string $actionId
     * @return bool
     */
    protected function isAuthRequired($actionId)
    {
        // Если действие в списке публичных - авторизация не нужна
        if (in_array($actionId, $this->publicActions)) {
            return false;
        }

        // Если действие в списке требующих авторизации - нужна авторизация
        if (in_array($actionId, $this->authRequired)) {
            return true;
        }

        // По умолчанию авторизация не требуется
        return false;
    }

    /**
     * Аутентификация пользователя по токену
     * 
     * @return Worker|null
     */
    protected function authenticateUser()
    {
        $token = $this->getTokenFromRequest();
        
        if (empty($token)) {
            return null;
        }

        return Worker::findByAuthToken($token);
    }

    /**
     * Получить токен из запроса
     * 
     * @return string|null
     */
    protected function getTokenFromRequest()
    {
        $request = \Yii::$app->request;

        // Проверяем заголовок Authorization
        $authHeader = $request->headers->get('Authorization');
        if ($authHeader && preg_match('/^Bearer\s+(.+)$/', $authHeader, $matches)) {
            return $matches[1];
        }

        // Проверяем параметр access_token
        $accessToken = $request->get('access_token');
        if ($accessToken) {
            return $accessToken;
        }

        return null;
    }

    /**
     * Отправить ответ об ошибке авторизации
     */
    protected function sendUnauthorizedResponse()
    {
        $response = \Yii::$app->response;
        $response->statusCode = 401;
        $response->format = \yii\web\Response::FORMAT_JSON;
        
        $apiResponse = ApiResponse::unauthorized('Authentication required');
        $response->data = $apiResponse->toArray();
        
        $response->send();
    }
}
