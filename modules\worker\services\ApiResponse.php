<?php

namespace app\modules\worker\services;

use yii\web\Response;

/**
 * Класс для стандартизированного возврата данных API
 * Обеспечивает консистентный формат ответов во всех API endpoints
 */
class ApiResponse
{
    /**
     * @var bool Статус успешности операции
     */
    public $success;
    
    /**
     * @var mixed Данные ответа
     */
    public $data;
    
    /**
     * @var string Сообщение (обычно для ошибок)
     */
    public $message;
    
    /**
     * @var array Дополнительная мета-информация
     */
    public $meta;
    
    /**
     * @var int HTTP статус код
     */
    public $statusCode;

    /**
     * Конструктор
     * 
     * @param bool $success
     * @param mixed $data
     * @param string $message
     * @param array $meta
     * @param int $statusCode
     */
    public function __construct($success = true, $data = null, $message = '', $meta = [], $statusCode = 200)
    {
        $this->success = $success;
        $this->data = $data;
        $this->message = $message;
        $this->meta = $meta;
        $this->statusCode = $statusCode;
    }

    /**
     * Создать успешный ответ
     * 
     * @param mixed $data
     * @param string $message
     * @param array $meta
     * @return static
     */
    public static function success($data = null, $message = '', $meta = [])
    {
        return new static(true, $data, $message, $meta, 200);
    }

    /**
     * Создать ответ с ошибкой
     * 
     * @param string $message
     * @param mixed $data
     * @param int $statusCode
     * @param array $meta
     * @return static
     */
    public static function error($message, $data = null, $statusCode = 400, $meta = [])
    {
        return new static(false, $data, $message, $meta, $statusCode);
    }

    /**
     * Создать ответ "не найдено"
     * 
     * @param string $message
     * @return static
     */
    public static function notFound($message = 'Resource not found')
    {
        return new static(false, null, $message, [], 404);
    }

    /**
     * Создать ответ "не авторизован"
     * 
     * @param string $message
     * @return static
     */
    public static function unauthorized($message = 'Unauthorized')
    {
        return new static(false, null, $message, [], 401);
    }

    /**
     * Создать ответ "запрещено"
     * 
     * @param string $message
     * @return static
     */
    public static function forbidden($message = 'Forbidden')
    {
        return new static(false, null, $message, [], 403);
    }

    /**
     * Создать ответ с ошибкой валидации
     * 
     * @param array $errors
     * @param string $message
     * @return static
     */
    public static function validationError($errors, $message = 'Validation failed')
    {
        return new static(false, $errors, $message, [], 422);
    }

    /**
     * Создать ответ с пагинацией
     * 
     * @param array $items
     * @param int $total
     * @param int $page
     * @param int $perPage
     * @param string $message
     * @return static
     */
    public static function paginated($items, $total, $page, $perPage, $message = '')
    {
        $meta = [
            'pagination' => [
                'total' => $total,
                'page' => $page,
                'per_page' => $perPage,
                'total_pages' => ceil($total / $perPage),
                'has_next' => $page < ceil($total / $perPage),
                'has_prev' => $page > 1
            ]
        ];
        
        return new static(true, $items, $message, $meta, 200);
    }

    /**
     * Преобразовать в массив для JSON ответа
     * 
     * @return array
     */
    public function toArray()
    {
        $result = [
            'success' => $this->success,
            'data' => $this->data,
        ];

        if (!empty($this->message)) {
            $result['message'] = $this->message;
        }

        if (!empty($this->meta)) {
            $result['meta'] = $this->meta;
        }

        return $result;
    }

    /**
     * Отправить JSON ответ
     * 
     * @param Response $response
     * @return Response
     */
    public function send($response = null)
    {
        if ($response === null) {
            $response = \Yii::$app->response;
        }

        $response->statusCode = $this->statusCode;
        $response->format = Response::FORMAT_JSON;
        $response->data = $this->toArray();

        return $response;
    }
}
