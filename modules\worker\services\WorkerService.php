<?php

namespace app\modules\worker\services;

use app\modules\worker\models\Worker;
use app\modules\worker\models\Profession;
use yii\web\UploadedFile;

/**
 * Сервис для работы с профилем работника
 */
class WorkerService
{
    /**
     * Получить профиль работника по номеру телефона
     * 
     * @param string $phone
     * @return array|null
     */
    public function getWorkerProfile($phone)
    {
        $worker = Worker::find()
            ->with(['professions'])
            ->where(['phone' => $phone, 'deleted_at' => null])
            ->one();

        if (!$worker) {
            return null;
        }

        return $this->formatWorkerProfile($worker);
    }

    /**
     * Получить профиль работника по ID
     * 
     * @param int $id
     * @return array|null
     */
    public function getWorkerProfileById($id)
    {
        $worker = Worker::find()
            ->with(['professions'])
            ->where(['id' => $id, 'deleted_at' => null])
            ->one();

        if (!$worker) {
            return null;
        }

        return $this->formatWorkerProfile($worker);
    }

    /**
     * Обновить профиль работника
     * 
     * @param string $phone
     * @param array $data
     * @return array|false
     */
    public function updateWorkerProfile($phone, $data)
    {
        $worker = Worker::findOne(['phone' => $phone, 'deleted_at' => null]);
        
        if (!$worker) {
            return false;
        }

        // Обновляем основные данные
        if (isset($data['name'])) {
            $worker->name = $data['name'];
        }
        
        if (isset($data['age'])) {
            $worker->age = $data['age'];
        }
        
        if (isset($data['experience_years'])) {
            $worker->experience_years = $data['experience_years'];
        }
        
        if (isset($data['about'])) {
            $worker->about = $data['about'];
        }
        
        if (isset($data['language'])) {
            $worker->language = $data['language'];
        }

        // Обновляем местоположение
        if (isset($data['lat']) && isset($data['lng'])) {
            $worker->lat = $data['lat'];
            $worker->long = $data['lng'];
        }

        // Валидация и сохранение
        if (!$worker->validate()) {
            return ['errors' => $worker->errors];
        }

        if (!$worker->save()) {
            return false;
        }

        // Обновляем профессии
        if (isset($data['profession_ids']) && is_array($data['profession_ids'])) {
            $this->updateWorkerProfessions($worker->id, $data['profession_ids']);
        }

        return $this->formatWorkerProfile($worker);
    }

    /**
     * Обновить профессии работника
     * 
     * @param int $workerId
     * @param array $professionIds
     */
    private function updateWorkerProfessions($workerId, $professionIds)
    {
        // Удаляем старые связи
        \Yii::$app->db->createCommand()
            ->delete('{{%worker_professions}}', ['worker_id' => $workerId])
            ->execute();

        // Добавляем новые связи
        if (!empty($professionIds)) {
            $rows = [];
            foreach ($professionIds as $professionId) {
                $rows[] = [$workerId, $professionId];
            }
            
            \Yii::$app->db->createCommand()
                ->batchInsert('{{%worker_professions}}', ['worker_id', 'profession_id'], $rows)
                ->execute();
        }
    }

    /**
     * Загрузить аудиофайл
     * 
     * @param string $phone
     * @param UploadedFile $file
     * @return array|false
     */
    public function uploadAudioFile($phone, $file)
    {
        $worker = Worker::findOne(['phone' => $phone, 'deleted_at' => null]);
        
        if (!$worker) {
            return false;
        }

        // Проверяем тип файла
        $allowedTypes = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg'];
        if (!in_array($file->type, $allowedTypes)) {
            return ['error' => 'Invalid file type'];
        }

        // Проверяем размер файла (максимум 10MB)
        if ($file->size > 10 * 1024 * 1024) {
            return ['error' => 'File too large'];
        }

        // Генерируем уникальное имя файла
        $fileName = 'worker_' . $worker->id . '_' . time() . '.' . $file->extension;
        $uploadPath = \Yii::getAlias('@webroot/uploads/audio/');
        
        // Создаем папку если не существует
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0755, true);
        }

        // Сохраняем файл
        $filePath = $uploadPath . $fileName;
        if ($file->saveAs($filePath)) {
            // Обновляем путь в базе данных
            $worker->audio_file_url = '/uploads/audio/' . $fileName;
            $worker->save();
            
            return ['audio_url' => $worker->audio_file_url];
        }

        return false;
    }

    /**
     * Проверить существование работника по номеру телефона
     * 
     * @param string $phone
     * @return bool
     */
    public function workerExists($phone)
    {
        return Worker::find()
            ->where(['phone' => $phone, 'deleted_at' => null])
            ->exists();
    }

    /**
     * Форматировать профиль работника для API
     * 
     * @param Worker $worker
     * @return array
     */
    private function formatWorkerProfile($worker)
    {
        $professions = [];
        foreach ($worker->professions as $profession) {
            $professions[] = [
                'id' => $profession->id,
                'name_ru' => $profession->name_ru,
                'name_uz' => $profession->name_uz,
                'name_en' => $profession->name_en,
            ];
        }

        return [
            'id' => $worker->id,
            'name' => $worker->name,
            'phone' => $worker->phone,
            'age' => $worker->age,
            'experience_years' => $worker->experience_years,
            'about' => $worker->about,
            'language' => $worker->language,
            'location' => [
                'lat' => $worker->lat,
                'lng' => $worker->long,
            ],
            'audio_file_url' => $worker->audio_file_url,
            'professions' => $professions,
            'created_at' => $worker->created_at,
        ];
    }
}
