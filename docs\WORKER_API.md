# Worker API Documentation

## Обзор

API модуля worker предоставляет endpoints для мобильного приложения соискателей работы. API поддерживает мультиязычность (ru, uz, en) и включает систему авторизации на основе токенов.

## Базовый URL

```
https://yourdomain.com/worker/
```

## Авторизация

API использует Bearer Token авторизацию. Токен передается в заголовке:

```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

Или как параметр запроса:
```
?access_token=YOUR_ACCESS_TOKEN
```

## Мультиязычность

Язык ответов определяется заголовком `Accept-Language`:

```
Accept-Language: ru
Accept-Language: uz  
Accept-Language: en
```

По умолчанию используется русский язык.

## Формат ответов

Все ответы возвращаются в JSON формате:

```json
{
  "success": true,
  "data": {...},
  "message": "Success message"
}
```

Для ошибок:
```json
{
  "success": false,
  "data": null,
  "message": "Error message"
}
```

Для пагинированных данных:
```json
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "total": 100,
      "page": 1,
      "per_page": 20,
      "total_pages": 5,
      "has_next": true,
      "has_prev": false
    }
  }
}
```

## Endpoints

### Авторизация

#### POST /worker/auth/login
Авторизация по номеру телефона

**Параметры:**
- `phone` (string, required) - Номер телефона

**Ответ:**
```json
{
  "success": true,
  "data": {
    "access_token": "token_string",
    "token_type": "Bearer",
    "expires_at": "2025-01-04 15:30:00",
    "worker": {
      "id": 1,
      "name": "Иван Иванов",
      "phone": "+998901234567",
      ...
    }
  }
}
```

#### POST /worker/auth/logout
Выход из системы (очистка токена)

**Требует авторизации**

#### GET /worker/auth/verify
Проверка валидности токена

**Требует авторизации**

#### POST /worker/auth/refresh
Обновление токена

**Требует авторизации**

### Вакансии

#### GET /worker/vacancy/list
Получить список вакансий

**Публичный доступ**

**Параметры:**
- `page` (int, optional) - Номер страницы (по умолчанию 1)
- `per_page` (int, optional) - Элементов на странице (по умолчанию 20, максимум 50)
- `profession_id` (int, optional) - Фильтр по профессии
- `min_salary` (int, optional) - Минимальная зарплата
- `lat` (float, optional) - Широта для поиска по местоположению
- `lng` (float, optional) - Долгота для поиска по местоположению  
- `radius` (int, optional) - Радиус поиска в км

**Ответ:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": 1,
        "title": "Программист PHP",
        "salary": "1000-1500 USD",
        "profession": {
          "id": 1,
          "name_ru": "Программист",
          "name_uz": "Dasturchi",
          "name_en": "Programmer"
        },
        "employer": {
          "id": 1,
          "business_name": "IT Company"
        },
        "location": {
          "lat": 41.2995,
          "lng": 69.2401
        },
        "created_at": "2025-06-28 12:00:00"
      }
    ],
    "pagination": {...}
  }
}
```

#### GET /worker/vacancy/search
Поиск вакансий

**Публичный доступ**

**Параметры:**
- `q` (string, optional) - Поисковый запрос
- Все параметры из `/vacancy/list`

#### GET /worker/vacancy/detail/{id}
Детальная информация о вакансии

**Требует авторизации**

**Ответ:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "Программист PHP",
    "description": "Полное описание вакансии...",
    "salary": "1000-1500 USD",
    "profession": {...},
    "employer": {
      "id": 1,
      "name": "Иван Петров",
      "business_name": "IT Company",
      "business_address": "г. Ташкент, ул. Амира Темура 1",
      "phone": "+998901234567"
    },
    "location": {...},
    "created_at": "2025-06-28 12:00:00"
  }
}
```

### Профиль работника

#### GET /worker/profile
Получить профиль работника

**Требует авторизации**

**Ответ:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Иван Иванов",
    "phone": "+998901234567",
    "age": 25,
    "experience_years": 3,
    "about": "Опытный разработчик...",
    "language": "ru",
    "location": {
      "lat": 41.2995,
      "lng": 69.2401
    },
    "audio_file_url": "/uploads/audio/worker_1_audio.mp3",
    "professions": [
      {
        "id": 1,
        "name_ru": "Программист",
        "name_uz": "Dasturchi", 
        "name_en": "Programmer"
      }
    ],
    "created_at": "2025-06-28 10:00:00"
  }
}
```

#### PUT /worker/profile
Обновить профиль работника

**Требует авторизации**

**Параметры:**
- `name` (string, optional) - Имя
- `age` (int, optional) - Возраст (16-80)
- `experience_years` (int, optional) - Опыт работы в годах (0-50)
- `about` (string, optional) - О себе
- `language` (string, optional) - Язык (ru, uz, en)
- `lat` (float, optional) - Широта
- `lng` (float, optional) - Долгота
- `profession_ids` (array, optional) - Массив ID профессий

#### POST /worker/profile/upload-audio
Загрузить аудиофайл

**Требует авторизации**

**Параметры:**
- `audio` (file, required) - Аудиофайл (mp3, wav, ogg, максимум 10MB)

## Коды ошибок

- `200` - Успешно
- `400` - Ошибка валидации
- `401` - Не авторизован
- `403` - Запрещено
- `404` - Не найдено
- `422` - Ошибка валидации данных
- `500` - Внутренняя ошибка сервера

## Примеры использования

### Авторизация и получение профиля

```bash
# Авторизация
curl -X POST "https://yourdomain.com/worker/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"phone": "+998901234567"}'

# Получение профиля
curl -X GET "https://yourdomain.com/worker/profile" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Поиск вакансий

```bash
# Публичный поиск
curl -X GET "https://yourdomain.com/worker/vacancy/search?q=программист&page=1&per_page=10"

# Детальный просмотр (требует авторизации)
curl -X GET "https://yourdomain.com/worker/vacancy/detail/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Обновление профиля

```bash
curl -X PUT "https://yourdomain.com/worker/profile" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Новое Имя",
    "age": 26,
    "about": "Обновленное описание",
    "profession_ids": [1, 2, 3]
  }'
```
