<?php

namespace app\modules\employer\models;

use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;

/**
 * Модель вакансии
 * 
 * @property int $id
 * @property int $employer_id
 * @property string $title
 * @property float $lat
 * @property float $long
 * @property string $description
 * @property string $salary
 * @property int $profession_id
 * @property int $status
 * @property string $created_at
 * @property string $deleted_at
 */
class Vacancy extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%vacancies}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => false,
                'value' => function() {
                    return date('Y-m-d H:i:s');
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['employer_id', 'title', 'description', 'profession_id'], 'required'],
            [['employer_id', 'profession_id', 'status'], 'integer'],
            [['lat', 'long'], 'number'],
            [['description'], 'string'],
            [['created_at', 'deleted_at'], 'safe'],
            [['title'], 'string', 'max' => 100],
            [['salary'], 'string', 'max' => 50],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'employer_id' => 'Работодатель',
            'title' => 'Название вакансии',
            'lat' => 'Широта',
            'long' => 'Долгота',
            'description' => 'Описание',
            'salary' => 'Зарплата',
            'profession_id' => 'Профессия',
            'status' => 'Статус',
            'created_at' => 'Дата создания',
            'deleted_at' => 'Дата удаления',
        ];
    }

    /**
     * Получение работодателя
     */
    public function getEmployer()
    {
        return $this->hasOne(Employer::class, ['id' => 'employer_id']);
    }

    /**
     * Получение профессии
     */
    public function getProfession()
    {
        return $this->hasOne(\app\modules\worker\models\Profession::class, ['id' => 'profession_id']);
    }
} 