<?php

use yii\db\Migration;

class m250626_075421_add_data_for_profession_table extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {

        $this->batchInsert('{{%professions}}', ['name_uz', 'name_ru', 'name_en'], [
            ['<PERSON><PERSON><PERSON><PERSON>', 'Водитель', 'Driver'],
            ['<PERSON><PERSON><PERSON>', '<PERSON>у<PERSON>ье<PERSON>', 'Courier'],
            ['<PERSON><PERSON><PERSON><PERSON>', 'Продавец', 'Salesperson'],
            ['Kassir', 'Кассир', 'Cashier'],
            ['Qo‘riqchi', 'Охранник', 'Security guard'],
            ['<PERSON><PERSON><PERSON>chi', 'Уборщик', 'Cleaner'],
            ['<PERSON><PERSON><PERSON>z', 'Повар', 'Cook'],
            ['Ofitsiant', 'Официант', 'Waiter'],
            ['<PERSON>k tashuvchi', 'Грузчик', 'Loader'],
            ['Quruvchi', 'Строитель', 'Builder'],
            ['<PERSON><PERSON><PERSON><PERSON>', 'Электрик', '<PERSON>ian'],
            ['<PERSON><PERSON><PERSON>', 'Сантехник', 'Plumber'],
            ['<PERSON>‘yoqchi', 'Маляр', 'Painter'],
            ['Mexanik', 'Слесарь', 'Fitter'],
            ['Payvandchi', 'Сварщик', 'Welder'],
            ['Dasturchi', 'Программист', 'Programmer'],
            ['Menejer', 'Менеджер', 'Manager'],
            ['Administrator', 'Администратор', 'Administrator'],
            ['Buxgalter', 'Бухгалтер', 'Accountant'],
            ['Dizayner', 'Дизайнер', 'Designer'],
        ]);
        
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        
    }

    
}
