<?php

namespace app\modules\telegram\controllers;

use yii\web\Controller;
use yii\web\Response;
use app\modules\worker\models\Profession;
use app\modules\telegram\models\TemporaryRegistration;

/**
 * Контроллер выбора профессий через WebView
 */
class ProfessionController extends Controller
{
    public $layout = false;

    /**
     * Страница выбора профессий
     */
    public function actionSelect($chat_id)
    {
        $professions = Profession::find()
            ->where(['deleted_at' => null])
            ->orderBy('name ASC')
            ->all();

        return $this->render('select', [
            'professions' => $professions,
            'chat_id' => $chat_id
        ]);
    }

    /**
     * Сохранение выбранной профессии
     */
    public function actionSave()
    {
        \Yii::$app->response->format = Response::FORMAT_JSON;

        $chatId = \Yii::$app->request->post('chat_id');
        $professionIds = \Yii::$app->request->post('profession_ids', []);

        if (!$chatId || empty($professionIds)) {
            return ['success' => false, 'message' => 'Не выбрана профессия'];
        }

        $registration = TemporaryRegistration::findByChatId($chatId);
        if (!$registration) {
            return ['success' => false, 'message' => 'Регистрация не найдена'];
        }

        // Сохраняем выбранные профессии (пока просто первую)
        $professionId = $professionIds[0];
        $profession = Profession::findOne($professionId);
        
        if ($profession) {
            // Переходим к следующему этапу
            $registration->status = TemporaryRegistration::STEP_AUDIO;
            $registration->save();

            // Сохраняем связь профессий в отдельной таблице (пока упрощенно)
            // TODO: сохранить в worker_professions после создания worker

            return ['success' => true, 'profession' => $profession->name];
        }

        return ['success' => false, 'message' => 'Профессия не найдена'];
    }
} 