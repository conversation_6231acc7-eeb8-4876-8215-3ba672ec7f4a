<?php

return [
    // Общие сообщения
    'Phone number is required' => 'Номер телефона обязателен',
    'Worker with this phone number not found' => 'Работник с таким номером телефона не найден',
    'Worker not found' => 'Работник не найден',
    'Successfully authenticated' => 'Успешная авторизация',
    'Token not provided' => 'Токен не предоставлен',
    'Successfully logged out' => 'Успешный выход из системы',
    'Invalid or expired token' => 'Недействительный или истекший токен',
    'Token is valid' => 'Токен действителен',
    'Token refreshed successfully' => 'Токен успешно обновлен',
    'Authentication required' => 'Требуется авторизация',
    
    // Вакансии
    'Vacancies retrieved successfully' => 'Вакансии успешно получены',
    'Error retrieving vacancies' => 'Ошибка при получении вакансий',
    'Search completed successfully' => 'Поиск успешно завершен',
    'Error searching vacancies' => 'Ошибка при поиске вакансий',
    'Invalid vacancy ID' => 'Неверный ID вакансии',
    'Vacancy not found' => 'Вакансия не найдена',
    'Vacancy details retrieved successfully' => 'Детали вакансии успешно получены',
    'Error retrieving vacancy details' => 'Ошибка при получении деталей вакансии',
    
    // Профиль
    'Profile not found' => 'Профиль не найден',
    'Profile retrieved successfully' => 'Профиль успешно получен',
    'Error retrieving profile' => 'Ошибка при получении профиля',
    'Failed to update profile' => 'Не удалось обновить профиль',
    'Profile updated successfully' => 'Профиль успешно обновлен',
    'Error updating profile' => 'Ошибка при обновлении профиля',
    
    // Валидация профиля
    'Name cannot be empty' => 'Имя не может быть пустым',
    'Name is too long' => 'Имя слишком длинное',
    'Age must be between 16 and 80' => 'Возраст должен быть от 16 до 80 лет',
    'Experience must be between 0 and 50 years' => 'Опыт работы должен быть от 0 до 50 лет',
    'Invalid language' => 'Неверный язык',
    'Both latitude and longitude are required' => 'Требуются и широта, и долгота',
    'Invalid latitude' => 'Неверная широта',
    'Invalid longitude' => 'Неверная долгота',
    
    // Аудио
    'Audio file is required' => 'Аудиофайл обязателен',
    'Failed to upload audio file' => 'Не удалось загрузить аудиофайл',
    'Invalid file type' => 'Неверный тип файла',
    'File too large' => 'Файл слишком большой',
    'Audio file uploaded successfully' => 'Аудиофайл успешно загружен',
    'Error uploading audio file' => 'Ошибка при загрузке аудиофайла',
    
    // Общие ошибки
    'Validation failed' => 'Ошибка валидации',
    'Resource not found' => 'Ресурс не найден',
    'Unauthorized' => 'Не авторизован',
    'Forbidden' => 'Запрещено',
];
