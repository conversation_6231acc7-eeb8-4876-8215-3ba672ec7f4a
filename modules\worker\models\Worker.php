<?php

namespace app\modules\worker\models;

use yii\db\ActiveRecord;
use yii\behaviors\TimestampBehavior;

/**
 * Модель работника
 * 
 * @property int $id
 * @property int $chat_id
 * @property string $name
 * @property string $phone
 * @property int $age
 * @property float $lat
 * @property float $long
 * @property int $experience_years
 * @property string $audio_file_url
 * @property string $about
 * @property string $language
 * @property int $status
 * @property string $created_at
 * @property string $deleted_at
 */
class Worker extends ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%workers}}';
    }

    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'createdAtAttribute' => 'created_at',
                'updatedAtAttribute' => false,
                'value' => function() {
                    return date('Y-m-d H:i:s');
                }
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['chat_id', 'age', 'experience_years', 'status'], 'integer'],
            [['lat', 'long'], 'number'],
            [['about'], 'string'],
            [['created_at', 'deleted_at'], 'safe'],
            [['name'], 'string', 'max' => 100],
            [['phone'], 'string', 'max' => 20],
            [['audio_file_url'], 'string', 'max' => 255],
            [['language'], 'string', 'max' => 10],
            [['chat_id'], 'unique'],
            [['phone'], 'unique'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'chat_id' => 'Telegram Chat ID',
            'name' => 'Имя',
            'phone' => 'Телефон',
            'age' => 'Возраст',
            'lat' => 'Широта',
            'long' => 'Долгота',
            'experience_years' => 'Опыт работы (лет)',
            'audio_file_url' => 'Аудиофайл',
            'about' => 'О себе',
            'language' => 'Язык',
            'status' => 'Статус',
            'created_at' => 'Дата создания',
            'deleted_at' => 'Дата удаления',
        ];
    }

    /**
     * Получение профессий работника
     */
    public function getProfessions()
    {
        return $this->hasMany(Profession::class, ['id' => 'profession_id'])
            ->viaTable('{{%worker_professions}}', ['worker_id' => 'id']);
    }

    /**
     * Получение избранных вакансий
     */
    public function getFavoriteVacancies()
    {
        return $this->hasMany(\app\modules\employer\models\Vacancy::class, ['id' => 'vacancy_id'])
            ->viaTable('{{%worker_favorites}}', ['worker_id' => 'id'])
            ->where(['{{%worker_favorites}}.deleted_at' => null]);
    }
} 