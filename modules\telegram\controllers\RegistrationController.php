<?php

namespace app\modules\telegram\controllers;

use yii\web\Controller;
use yii\web\Response;
use app\modules\telegram\models\TemporaryRegistration;
use app\modules\telegram\models\TelegramService;
use app\modules\worker\models\Profession;
use app\common\models\WorkerLog;

/**
 * Контроллер регистрации работника через Telegram
 */
class RegistrationController extends Controller
{
    /**
     * @var array Отключение CSRF
     */
    public $enableCsrfValidation = false;

    /**
     * @var TelegramService Сервис Telegram
     */
    private $telegramService;

    /**
     * {@inheritdoc}
     */
    public function init()
    {
        parent::init();
        $this->telegramService = \Yii::$app->get('telegramService');
    }

    /**
     * Обработка этапов регистрации
     */
    public function actionProcess()
    {
        $input = file_get_contents('php://input');
        $update = json_decode($input, true);

        if (!$update) {
            return $this->asJson(['ok' => false]);
        }

        $chatId = $this->getChatId($update);
        if (!$chatId) {
            return $this->asJson(['ok' => false]);
        }

        // Получаем или создаем регистрацию
        $registration = TemporaryRegistration::findByChatId($chatId);
        if (!$registration) {
            $registration = TemporaryRegistration::createOrUpdate($chatId);
        }

        // Обрабатываем команду /start
        if (isset($update['message']['text']) && $update['message']['text'] === '/start') {
            return $this->handleStart($chatId, $registration);
        }

        // Обрабатываем callback query (кнопки)
        if (isset($update['callback_query'])) {
            return $this->handleCallback($update['callback_query'], $registration);
        }

        // Обрабатываем текстовое сообщение
        if (isset($update['message']['text'])) {
            return $this->handleTextMessage($update['message'], $registration);
        }

        // Обрабатываем геолокацию
        if (isset($update['message']['location'])) {
            return $this->handleLocation($update['message'], $registration);
        }

        // Обрабатываем аудио
        if (isset($update['message']['voice'])) {
            return $this->handleVoice($update['message'], $registration);
        }

        return $this->asJson(['ok' => true]);
    }

    /**
     * Обработка команды /start
     */
    private function handleStart($chatId, $registration)
    {
        $registration->status = TemporaryRegistration::STEP_LANGUAGE;
        $registration->save();

        // Логируем начало регистрации
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_START, [
            'registration_id' => $registration->id
        ]);

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '🇷🇺 Русский', 'callback_data' => 'lang_ru'],
                    ['text' => '🇺🇿 O\'zbekcha', 'callback_data' => 'lang_uz'],
                ],
                [
                    ['text' => '🇬🇧 English', 'callback_data' => 'lang_en'],
                ]
            ]
        ];

        $text = "👋 Добро пожаловать в IshTop!\n\nВыберите язык:";
        $this->telegramService->sendMessage($chatId, $text, $keyboard);
        
        return $this->asJson(['ok' => true]);
    }

    /**
     * Обработка callback query
     */
    private function handleCallback($callbackQuery, $registration)
    {
        $data = $callbackQuery['data'];
        $chatId = $callbackQuery['from']['id'];

        if (strpos($data, 'lang_') === 0) {
            return $this->handleLanguageSelection($chatId, $registration, $data);
        }

        if ($data === 'confirm_registration') {
            return $this->handleConfirmRegistration($chatId, $registration);
        }

        if ($data === 'additional_info') {
            return $this->handleAdditionalInfo($chatId, $registration);
        }

        if ($data === 'profession_selected') {
            return $this->handleProfessionSelected($chatId, $registration);
        }

        return $this->asJson(['ok' => true]);
    }

    /**
     * Выбор языка
     */
    private function handleLanguageSelection($chatId, $registration, $langData)
    {
        $language = str_replace('lang_', '', $langData);
        $registration->language = $language;
        $registration->status = TemporaryRegistration::STEP_NAME;
        $registration->save();

        // Логируем выбор языка
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_LANGUAGE, [
            'language' => $language,
            'registration_id' => $registration->id
        ]);

        $messages = [
            'ru' => "✅ Язык выбран: Русский\n\n👤 Как вас зовут? Введите ваше имя:",
            'uz' => "✅ Til tanlandi: O'zbekcha\n\n👤 Ismingiz nima? Ismingizni kiriting:",
            'en' => "✅ Language selected: English\n\n👤 What is your name? Enter your name:",
        ];

        $text = $messages[$language] ?? $messages['ru'];
        $this->telegramService->sendMessage($chatId, $text);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Обработка текстового сообщения
     */
    private function handleTextMessage($message, $registration)
    {
        $chatId = $message['chat']['id'];
        $text = trim($message['text']);

        switch ($registration->status) {
            case TemporaryRegistration::STEP_NAME:
                return $this->handleNameInput($chatId, $registration, $text);
            
            case TemporaryRegistration::STEP_PHONE:
                return $this->handlePhoneInput($chatId, $registration, $text);
            
            case TemporaryRegistration::STEP_AGE:
                return $this->handleAgeInput($chatId, $registration, $text);
        }

        return $this->asJson(['ok' => true]);
    }

    /**
     * Ввод имени
     */
    private function handleNameInput($chatId, $registration, $name)
    {
        $registration->name = $name;
        $registration->status = TemporaryRegistration::STEP_PHONE;
        $registration->save();

        // Логируем ввод имени
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_NAME, [
            'name' => $name,
            'registration_id' => $registration->id
        ]);

        $messages = [
            'ru' => "✅ Имя: {$name}\n\n📱 Введите ваш номер телефона:",
            'uz' => "✅ Ism: {$name}\n\n📱 Telefon raqamingizni kiriting:",
            'en' => "✅ Name: {$name}\n\n📱 Enter your phone number:",
        ];

        $text = $messages[$registration->language] ?? $messages['ru'];
        $this->telegramService->sendMessage($chatId, $text);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Ввод телефона
     */
    private function handlePhoneInput($chatId, $registration, $phone)
    {
        $registration->phone = $phone;
        $registration->status = TemporaryRegistration::STEP_LOCATION;
        $registration->save();

        // Логируем ввод телефона
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_PHONE, [
            'phone' => $phone,
            'registration_id' => $registration->id
        ]);

        $keyboard = [
            'keyboard' => [
                [
                    ['text' => '📍 Отправить геолокацию', 'request_location' => true]
                ]
            ],
            'resize_keyboard' => true,
            'one_time_keyboard' => true
        ];

        $messages = [
            'ru' => "✅ Телефон: {$phone}\n\n📍 Отправьте вашу геолокацию для поиска работы поблизости:",
            'uz' => "✅ Telefon: {$phone}\n\n📍 Yaqin atrofdagi ish topish uchun joylashuvingizni yuboring:",
            'en' => "✅ Phone: {$phone}\n\n📍 Send your location to find nearby jobs:",
        ];

        $text = $messages[$registration->language] ?? $messages['ru'];
        $this->telegramService->sendMessage($chatId, $text, $keyboard);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Обработка геолокации
     */
    private function handleLocation($message, $registration)
    {
        $chatId = $message['chat']['id'];
        $location = $message['location'];

        $registration->lat = $location['latitude'];
        $registration->long = $location['longitude'];
        $registration->status = TemporaryRegistration::STEP_AGE;
        $registration->save();

        // Логируем отправку геолокации
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_LOCATION, [
            'latitude' => $location['latitude'],
            'longitude' => $location['longitude'],
            'registration_id' => $registration->id
        ]);

        // Убираем клавиатуру
        $removeKeyboard = ['remove_keyboard' => true];

        $messages = [
            'ru' => "✅ Геолокация получена\n\n🎂 Сколько вам лет? Введите ваш возраст:",
            'uz' => "✅ Joylashuv qabul qilindi\n\n🎂 Necha yoshdasiz? Yoshingizni kiriting:",
            'en' => "✅ Location received\n\n🎂 How old are you? Enter your age:",
        ];

        $text = $messages[$registration->language] ?? $messages['ru'];
        $this->telegramService->sendMessage($chatId, $text, $removeKeyboard);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Ввод возраста
     */
    private function handleAgeInput($chatId, $registration, $ageText)
    {
        $age = intval($ageText);
        
        if ($age < 16 || $age > 80) {
            $messages = [
                'ru' => "❌ Пожалуйста, введите корректный возраст (16-80 лет):",
                'uz' => "❌ Iltimos, to'g'ri yoshni kiriting (16-80 yosh):",
                'en' => "❌ Please enter a valid age (16-80 years):",
            ];
            
            $text = $messages[$registration->language] ?? $messages['ru'];
            $this->telegramService->sendMessage($chatId, $text);
            return $this->asJson(['ok' => true]);
        }

        $registration->age = $age;
        $registration->status = TemporaryRegistration::STEP_PROFESSION;
        $registration->save();

        // Логируем ввод возраста
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_AGE, [
            'age' => $age,
            'registration_id' => $registration->id
        ]);

        // Открываем WebView для выбора профессии
        $webViewUrl = \Yii::$app->urlManager->createAbsoluteUrl(['/telegram/profession/select', 'chat_id' => $chatId]);
        
        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '🔧 Выбрать деятельность', 'web_app' => ['url' => $webViewUrl]]
                ]
            ]
        ];

        $messages = [
            'ru' => "✅ Возраст: {$age} лет\n\n🔧 Теперь выберите вашу деятельность (профессию):",
            'uz' => "✅ Yosh: {$age} yosh\n\n🔧 Endi faoliyatingizni (kasbingizni) tanlang:",
            'en' => "✅ Age: {$age} years\n\n🔧 Now choose your activity (profession):",
        ];

        $text = $messages[$registration->language] ?? $messages['ru'];
        $this->telegramService->sendMessage($chatId, $text, $keyboard);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Обработка аудиозаписи
     */
    private function handleVoice($message, $registration)
    {
        $chatId = $message['chat']['id'];
        $voice = $message['voice'];

        // Скачиваем аудиофайл
        $audioUrl = $this->downloadVoiceFile($voice['file_id']);
        
        if ($audioUrl) {
            $registration->audio_file_url = $audioUrl;
            $registration->status = TemporaryRegistration::STEP_CONFIRMATION;
            $registration->save();

            // Логируем загрузку аудио
            WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_AUDIO, [
                'audio_file_url' => $audioUrl,
                'file_id' => $voice['file_id'],
                'duration' => $voice['duration'] ?? null,
                'registration_id' => $registration->id
            ]);

            $keyboard = [
                'inline_keyboard' => [
                    [
                        ['text' => '✅ Подтвердить регистрацию', 'callback_data' => 'confirm_registration']
                    ],
                    [
                        ['text' => '📝 Заполнить доп. информацию', 'callback_data' => 'additional_info']
                    ]
                ]
            ];

            $messages = [
                'ru' => "✅ Аудиозапись получена!\n\n🎯 Регистрация почти завершена. Выберите действие:",
                'uz' => "✅ Audio yozuv qabul qilindi!\n\n🎯 Ro'yxatdan o'tish deyarli tugadi. Harakatni tanlang:",
                'en' => "✅ Audio recording received!\n\n🎯 Registration is almost complete. Choose an action:",
            ];

            $text = $messages[$registration->language] ?? $messages['ru'];
            $this->telegramService->sendMessage($chatId, $text, $keyboard);
        }

        return $this->asJson(['ok' => true]);
    }

    /**
     * Подтверждение регистрации
     */
    private function handleConfirmRegistration($chatId, $registration)
    {
        // Переносим данные в основную таблицу workers
        $worker = $this->createWorkerFromRegistration($registration);

        $registration->status = TemporaryRegistration::STEP_COMPLETED;
        $registration->save();

        // Логируем завершение регистрации
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_COMPLETED, [
            'worker_id' => $worker ? $worker->id : null,
            'registration_id' => $registration->id
        ], $worker ? $worker->id : null);

        // Определяем платформу и отправляем ссылки на приложения
        $downloadLinks = $this->getDownloadLinks();

        $messages = [
            'ru' => "🎉 Поздравляем! Регистрация завершена!\n\n📱 Скачайте наше приложение:\n\n{$downloadLinks}",
            'uz' => "🎉 Tabriklaymiz! Ro'yxatdan o'tish tugallandi!\n\n📱 Ilovamizni yuklab oling:\n\n{$downloadLinks}",
            'en' => "🎉 Congratulations! Registration completed!\n\n📱 Download our app:\n\n{$downloadLinks}",
        ];

        $text = $messages[$registration->language] ?? $messages['ru'];
        $this->telegramService->sendMessage($chatId, $text);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Создание работника из временной регистрации
     */
    private function createWorkerFromRegistration($registration)
    {
        $worker = new \app\modules\worker\models\Worker();
        $worker->chat_id = $registration->chat_id;
        $worker->name = $registration->name;
        $worker->phone = $registration->phone;
        $worker->age = $registration->age;
        $worker->lat = $registration->lat;
        $worker->long = $registration->long;
        $worker->experience_years = $registration->experience_years;
        $worker->audio_file_url = $registration->audio_file_url;
        $worker->about = $registration->about;
        $worker->language = $registration->language;
        $worker->status = 1; // Активный
        
        if ($worker->save()) {
            return $worker;
        }
        
        return null;
    }

    /**
     * Скачивание аудиофайла
     */
    private function downloadVoiceFile($fileId)
    {
        // Получаем информацию о файле
        $fileInfo = $this->telegramService->makeRequest('getFile', ['file_id' => $fileId]);
        
        if (!$fileInfo['ok']) {
            return false;
        }

        $filePath = $fileInfo['result']['file_path'];
        $fileUrl = "https://api.telegram.org/file/bot{$this->telegramService->botToken}/{$filePath}";

        // Создаем папку для аудио
        $audioDir = \Yii::getAlias('@webroot/uploads/audio');
        if (!is_dir($audioDir)) {
            mkdir($audioDir, 0755, true);
        }

        // Генерируем имя файла
        $fileName = time() . '_' . uniqid() . '.ogg';
        $localPath = $audioDir . '/' . $fileName;

        // Скачиваем файл
        if (copy($fileUrl, $localPath)) {
            return '/uploads/audio/' . $fileName;
        }

        return false;
    }

    /**
     * Получение ссылок на скачивание приложения
     */
    private function getDownloadLinks()
    {
        return "🤖 Android: https://play.google.com/store/apps/details?id=com.ishtop.app\n" .
               "🍎 iOS: https://apps.apple.com/app/ishtop/id123456789\n" .
               "🌐 Web: https://app.ishtop.uz";
    }

    /**
     * Получение chat_id из update
     */
    private function getChatId($update)
    {
        if (isset($update['message']['chat']['id'])) {
            return $update['message']['chat']['id'];
        }
        
        if (isset($update['callback_query']['from']['id'])) {
            return $update['callback_query']['from']['id'];
        }

        return null;
    }

    /**
     * Обработка выбора профессии
     */
    private function handleProfessionSelected($chatId, $registration)
    {
        $registration->status = TemporaryRegistration::STEP_AUDIO;
        $registration->save();

        // Логируем переход к этапу выбора профессии
        WorkerLog::logRegistrationStep($chatId, TemporaryRegistration::STEP_PROFESSION, [
            'registration_id' => $registration->id,
            'webview_opened' => 'true'
        ]);

        $messages = [
            'ru' => "✅ Профессия выбрана!\n\n🎤 Теперь расскажите о себе голосовым сообщением (до 60 секунд):",
            'uz' => "✅ Kasb tanlandi!\n\n🎤 Endi o'zingiz haqingizda ovozli xabar bilan gapirib bering (60 sekundgacha):",
            'en' => "✅ Profession selected!\n\n🎤 Now tell us about yourself with a voice message (up to 60 seconds):",
        ];

        $text = $messages[$registration->language] ?? $messages['ru'];
        $this->sendMessage($chatId, $text);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Обработка дополнительной информации
     */
    private function handleAdditionalInfo($chatId, $registration)
    {
        $registration->status = TemporaryRegistration::STEP_ADDITIONAL_INFO;
        $registration->save();

        $messages = [
            'ru' => "📝 Введите дополнительную информацию о вашем опыте работы:",
            'uz' => "📝 Ish tajribangiz haqida qo'shimcha ma'lumot kiriting:",
            'en' => "📝 Enter additional information about your work experience:",
        ];

        $text = $messages[$registration->language] ?? $messages['ru'];
        $this->sendMessage($chatId, $text);

        return $this->asJson(['ok' => true]);
    }

    /**
     * Отправка сообщения (упрощенная версия)
     */
    private function sendMessage($chatId, $text, $keyboard = null)
    {
        if ($this->telegramService) {
            return $this->telegramService->sendMessage($chatId, $text, $keyboard);
        }

        // Fallback для логирования
        \Yii::info("Send message to {$chatId}: {$text}", 'telegram');
        return ['ok' => true];
    }
} 